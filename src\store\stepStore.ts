import { defineStore } from 'pinia'
import { ref } from 'vue'
import { CreateStep, ScanStep } from '@/utils/enum'
import { AddressForm } from '@/store/dbStore'

export const stepStore = defineStore(
  'stepStore',
  () => {
    // 新建页面步骤
    const createStep = ref<CreateStep>(CreateStep.createTask)
    // 当前任务id
    const activeTaskId = ref('')
    const activeTaskName = ref('')
    // 当前地址id
    const activeAddressId = ref('')
    // 当前任务下的地址库指针
    const addressPointerArr = ref<AddressForm[]>([])

    return {
      createStep,
      activeTaskId,
      activeTaskName,
      activeAddressId,
    }
  },
  {
    persist: true,
  },
)
