<script lang="ts" setup>
import { useMessage, useToast } from 'wot-design-uni'
import { stepStore } from '@/store/stepStore'
import { storeToRefs } from 'pinia'
import { CreateStep } from '@/utils/enum'
import { dbHook } from '@/hooks/dbHook'
import ChooseFile from '@/components/index/chooseFile.vue'
import { TaskForm } from '@/store/dbStore'

const message = useMessage()
const toast = useToast()
const useStepStore = stepStore()
const { createStep, activeTaskId, activeTaskName } = storeToRefs(useStepStore)

const {
  taskDataDb,
  addressDataDb,
  equDataDb,
  saveFileData,
  delTask,
  updateTask,
} = dbHook()

// console.log('taskDataDb', taskDataDb.value)
// console.log('addressDataDb', addressDataDb.value)
// console.log('equDataDb', equDataDb.value)

const taskName = ref('')

const handleDelTask = (id: string, name: string) => {
  message
    .confirm({
      msg: name,
      title: '是否删除任务',
    })
    .then(() => {
      delTask(id)
    })
    .catch(() => {})
}
const handleSelectTask = (id: string, name: string) => {
  activeTaskId.value = id
  activeTaskName.value = name
  createStep.value = CreateStep.createScanList
}

const handleModifyItem = (data: TaskForm) => {
  taskName.value = data.name
  message
    .prompt({
      title: '修改任务名',
      inputValue: data.name,
    })
    .then((resp) => {
      updateTask(data.id, resp.value + '')
    })
    .catch((error) => {
      console.log(error)
    })
}

const readFileFinish = (data: any) => {
  saveFileData(data)
}
</script>
<template>
  <wd-toast />
  <wd-message-box></wd-message-box>
  <view class="w-full bg-white fixed top-0 left-0 z-1 o-shadow">
    <view class="mt-2 text-center text-2xl">
      <view class="font-bold">友恒网络科技</view>
      <view>资产盘点手持应用</view>
    </view>
    <view class="p-4">
      <ChooseFile @readFileFinish="readFileFinish"></ChooseFile>
    </view>
  </view>
  <view class="pt-38 px-3 pb-20">
    <view
      class="rd-2 bg-white mb-2 o-shadow"
      v-for="item in taskDataDb"
      :key="item.id"
    >
      <view class="p-4">
        <view class="color-gray">创建时间：{{ item.createDate }}</view>
        <view class="font-bold text-lg">{{ item.name }}</view>
      </view>
      <view class="flex justify-between px-4 pb-4">
        <view
          class="o-btn mr-2 o-border color-red"
          type="error"
          plain
          @click="handleDelTask(item.id, item.name)"
        >
          删除
        </view>
        <view
          class="o-btn mr-2 o-border color-blue"
          @click="handleModifyItem(item)"
        >
          修改
        </view>
        <view
          class="o-btn bg-blue color-white grow-1"
          @click="handleSelectTask(item.id, item.name)"
        >
          选择任务
        </view>
      </view>
    </view>
  </view>
</template>
<style lang="scss" scoped>
$topHeight: 20vw;

.f-top-bar {
  height: $topHeight;
}

.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}
.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>
