import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: '盘点RFA',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
    backgroundColor: '#FFFFFF',
  },
  easycom: {
    autoscan: true,
    custom: {
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue',
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
    },
  },
  tabBar: {
    color: '#999999',
    selectedColor: '#00116F',
    backgroundColor: '#F8F8F8',
    borderStyle: 'black',
    height: '50px',
    fontSize: '10px',
    iconWidth: '24px',
    spacing: '3px',
    list: [
      {
        iconPath: 'static/tabbar/bar_up.png',
        selectedIconPath: 'static/tabbar/bar_up_c.png',
        pagePath: 'pages/index/index',
        text: '新建',
      },
      {
        iconPath: 'static/tabbar/bar_scan.png',
        selectedIconPath: 'static/tabbar/bar_scan_c.png',
        pagePath: 'pages/makeInventory/index',
        text: '清点',
      },
      {
        iconPath: 'static/tabbar/bar_down.png',
        selectedIconPath: 'static/tabbar/bar_down_c.png',
        pagePath: 'pages/exportPage/index',
        text: '导出',
      },
    ],
  },
})
