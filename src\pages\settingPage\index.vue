<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '配置',
  },
}
</route>
<script lang="ts" setup>
import { dbHook } from '@/hooks/dbHook'
import { useMessage, useToast } from 'wot-design-uni'
import { authorizationStore } from '@/store/authorizationStore'
import { storeToRefs } from 'pinia'

const message = useMessage()
const { resetDb } = dbHook()
const toast = useToast()
const useAuthorizationStore = authorizationStore()
const { sn } = storeToRefs(useAuthorizationStore)

const powerColumns = [
  500, 600, 700, 800, 900, 1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800,
  1900, 2000, 2100, 2200, 2300, 2400, 2500, 2600, 2700, 2800, 2900, 3000,
]
const model = reactive<{
  readReadPower: number
  readWritePower: number
  scanPower: number
}>({
  readReadPower: 500,
  readWritePower: 2000,
  scanPower: 3000,
})

onMounted(() => {
  uni.getStorage({
    key: 'o_powerValue',
    success: function (res) {
      console.log(res.data)
      // 将res.data按逗号拆分
      const [readReadPower, readWritePower, scanPower] = res.data.split(',')
      model.readReadPower = Number(readReadPower)
      model.readWritePower = Number(readWritePower)
      model.scanPower = Number(scanPower)
    },
  })
})

const form = ref()

const handleReset = () => {
  message
    .confirm({
      msg: '请注意导出备份好清点数据',
      title: '是否清空所有数据？',
    })
    .then(() => {
      resetDb()
    })
}

const handleSave = () => {
  const powerValue =
    model.readReadPower + ',' + model.readWritePower + ',' + model.scanPower
  try {
    uni.setStorageSync('o_powerValue', powerValue)
    toast.success('保存成功')
  } catch (e) {
    toast.success('保存失败')
  }
}
</script>
<template>
  <view class="p-4">
    <wd-button
      class="mb-4"
      block
      size="large"
      type="error"
      @click="handleReset"
    >
      重置所有数据
    </wd-button>
    <view class="rd-2 bg-white mb-2 o-shadow p-4">
      <wd-cell-group border>
        <view class="font-bold text-lg mb-2">RFID写入模式</view>
        <wd-picker
          :columns="powerColumns"
          label="写功率"
          v-model="model.readReadPower"
        />
        <wd-picker
          :columns="powerColumns"
          label="读功率"
          v-model="model.readWritePower"
        />
        <view class="font-bold text-lg mt-4 mb-2">盘点模式</view>
        <wd-picker
          :columns="powerColumns"
          label="读功率"
          v-model="model.scanPower"
        />
      </wd-cell-group>
    </view>
    <wd-button class="mt-4" block size="large" @click="handleSave">
      保存
    </wd-button>
    <view class="mt-16 text-center text-2xl">
      <view class="font-bold">友恒网络科技</view>
      <view>出品</view>
      <view class="text-sm">sn：{{ sn }}</view>
    </view>
  </view>
</template>
<style lang="scss" scoped></style>
