import { defineStore } from 'pinia'
import { ref } from 'vue'

// 任务表
export type TaskForm = {
  id: string
  name: string
  createDate: string
  // 已解决的地址冲突的设备Code，但凡出现在unplannedCodeArr的，
  // 且手动修改归属地点后，后续扫描不再出现到unplannedCodeArr中
  conflictResolvedEquCodeArr: string[]
}

export type AddressFormBase = {
  name: string
  taskId: string
  planCodeArr: string[] // 计划清点
  completedCodeArr: string[] // 已清点
  unfinishedCodeArr: string[] // 未清点
  unplannedCodeArr: string[] // 额外发现
  completedPercent: number // 已清点百分比
}

// 地址表
export interface AddressForm extends AddressFormBase {
  id: string
}

// 设备基础类
export type EquipmentFormBase = {
  code: string // 设备编号，只是任务中唯一，多个任务会重复，所以要id
  name: string // 设备名称
  addressId: string // 地址id
  addressName: string // 地址名称
  modelType: string // 规格型号
  userName: string // 使用人
  department: string // 使用部门
  hadPrintRfid: boolean // 是否已写入RFID
  hadModify_name: boolean // 是否已修改设备名
  hadModify_modelType: boolean // 是否已修改规格型号
  hadModify_userName: boolean // 是否已修改使用人
  hadModify_address: boolean // 是否已修改地址
  hadModify_department: boolean // 是否已修改使用部门
  scanNum: number // 扫描次数
}

// 可选设备基础类，用于修改数据
export type OptionalEquipmentFormBase = Partial<EquipmentFormBase>

// 设备表类
export interface EquipmentForm extends EquipmentFormBase {
  id: string
}

export const dbStore = defineStore(
  'dbStore',
  () => {
    const taskDataDb = ref<TaskForm[]>([])
    const addressDataDb = ref<AddressForm[]>([])
    const equDataDb = ref<EquipmentForm[]>([])

    return {
      taskDataDb,
      addressDataDb,
      equDataDb,
    }
  },
  {
    persist: true,
  },
)
