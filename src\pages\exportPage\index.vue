<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '导出',
  },
}
</route>
<script lang="ts" setup>
import { dbHook } from '@/hooks/dbHook'
import { useMessage } from 'wot-design-uni'
import { stepStore } from '@/store/stepStore'
import { storeToRefs } from 'pinia'
import { toStepOne } from '@/utils/tool'
import { getColor, rgbaToHex } from '@/utils/colorUtils'
import { pointerStore } from '@/store/pointerStore'
import dayjs from 'dayjs'

const rfidModule = uni.requireNativePlugin('rfidModule')

const message = useMessage()
const { resetDb } = dbHook()
const useStepStore = stepStore()
const { activeTaskName, activeTaskId } = storeToRefs(useStepStore)
const usePointerStore = pointerStore()
const { activeAddressList, activeEquipmentList } = storeToRefs(usePointerStore)

const taskPlanCount = ref(0)
const taskCompletedCount = ref(0)
const taskUnfinishedCount = ref(0)
const taskCompletedPercent = ref(0)
const successTip = ref('')

onShow(() => {
  getCountData()
  // 本页关闭RFID
  // rfidModule.toDisConnectDevice({}, (ret) => {})
})

const getCountData = () => {
  taskCompletedCount.value = 0
  taskUnfinishedCount.value = 0
  taskPlanCount.value = activeEquipmentList.value.length
  activeAddressList.value.forEach((item) => {
    taskCompletedCount.value += item.completedCodeArr.length
  })
  activeAddressList.value.forEach((item) => {
    taskUnfinishedCount.value += item.unfinishedCodeArr.length
  })
  if (taskPlanCount.value !== 0) {
    taskCompletedPercent.value = Math.floor(
      (taskCompletedCount.value / taskPlanCount.value) * 100,
    )
  } else {
    taskCompletedPercent.value = 0
  }
}

const handleReset = () => {
  message
    .confirm({
      msg: '请注意导出备份好清点数据',
      title: '是否清空所有数据？',
    })
    .then(() => {
      resetDb()
    })
}

const handleOutput = () => {
  const title = [
    '资产名称',
    '规格型号',
    '条形码',
    '使用人',
    '存放地点',
    '使用部门',
    '是否清点',
    '是否更变资产名',
    '是否变更规格型号',
    '是否变更使用人',
    '是否变更存放地点',
    '是否变更使用部门',
  ]
  const data: string[][] = [title]
  // 已清点
  const isCompletedArr = []
  activeAddressList.value.forEach((item) => {
    isCompletedArr.push(...item.completedCodeArr)
  })

  activeEquipmentList.value.forEach((item) => {
    // 是否已清点
    const isCompleted = isCompletedArr.includes(item.code)
    data.push([
      item.name, // 资产名称
      item.modelType, // 规格型号
      '`' + item.code, // 条形码
      item.userName, // 使用人
      item.addressName, // 存放地点
      item.department, // 使用部门
      isCompleted ? '已清点' : '未清点', // 是否清点
      item.hadModify_name ? '变更' : '', // 是否更变资产名
      item.hadModify_modelType ? '变更' : '', // 是否变更规格型号
      item.hadModify_userName ? '变更' : '', // 是否变更使用人
      item.hadModify_address ? '变更' : '', // 是否变更存放地点
      item.hadModify_department ? '变更' : '', // 是否变更使用部门
    ])
  })

  const worksheet = 'sheet1'
  let str = ''

  // 循环遍历，每行加入tr标签，每个单元格加td标签
  for (let i = 0; i < data.length; i++) {
    str += '<tr>'
    for (let k = 0; k < data[i].length; k++) {
      // 增加\t为了不让表格显示科学计数法或者其他格式
      str += `<td>${data[i][k] + '\t'}</td>`
    }
    str += '</tr>'
  }
  // 下载的表格模板数据
  const template = `<html xmlns:o="urn:schemas-microsoft-com:office:office"
        xmlns:x="urn:schemas-microsoft-com:office:excel"
        xmlns="http://www.w3.org/TR/REC-html40">
        <head><!--[if gte mso 9]><xml encoding="UTF-8"><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet>
        <x:Name>${worksheet}</x:Name>
        <x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet>
        </x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->
        </head><body><table>${str}</table></body></html>`

  exportFile(template)
}

// 导出文件到手机 fileData:要写入到文件的数据，返回参数为文档路径
function exportFile(fileData: any, documentName = '资产清点导出') {
  /*
        PRIVATE_DOC: 应用私有文档目录常量
        PUBLIC_DOCUMENTS: 程序公用文档目录常量
        */
  // console.log(plus.io)

  plus.io.resolveLocalFileSystemURL('_downloads/', function (entry) {
    // const fullPath = rootObj.fullPath
    // const pathStr = fullPath.replace('/storage/emulated/0', '')
    const fileName = `清点统计表${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`
    entry.getFile(
      fileName,
      { create: true },
      function (dirEntry) {
        dirEntry.createWriter(
          function (writer) {
            console.log('writer', writer)
            writer.onwritestart = () => {
              uni.showLoading({
                title: '正在导出',
                mask: true,
              })
            }

            writer.onwrite = function () {
              uni.hideLoading()
              successTip.value = `${entry.fullPath.replace('/storage/emulated/0', '')}${fileName}`
            }
            writer.onerror = function (e) {
              // console.error('写入文件失败：' + e.message)
            }
            // 写入内容
            writer.write(fileData)
          },
          function (e) {
            console.log(e.message)
          },
        )
      },
      function (e) {
        console.error('获取文件失败：' + e.message)
      },
    )
  })
}

const toSettingPage = () => {
  uni.navigateTo({
    url: '/pages/settingPage/index',
  })
}
</script>
<template>
  <view class="p-4">
    <wd-button block size="large" plain type="info" @click="toSettingPage">
      前往配置
    </wd-button>
    <view class="p-4 text-xs">
      <view class="mt-4 mb-2 font-bold">注意事项：</view>
      <ul>
        <li>
          清点中额外发现的资产，只要判定（修改）了地址，那么将不会再出现在其他额外发现中。
        </li>
        <li>
          开始清点任务后，建议不要再到【新建】页面修改资产的地址，除非您明确你修改的资产是否受上一条注意事项所影响。
        </li>
      </ul>
    </view>
    <view v-if="activeTaskId === ''" class="f-no center mt-12">
      <view class="o-btn bg-blue color-white text-lg" @click="toStepOne">
        请先新建或选择任务
      </view>
    </view>
    <view v-else>
      <view class="rd-2 bg-white mb-2 o-shadow p-4">
        <view class="mt-2 mb-3 text-2xl font-bold">任务统计情况</view>
        <view class="f-babel">
          <view class="f-label-name">任务名：</view>
          <view>{{ activeTaskName }}</view>
        </view>
        <view class="flex gap-4 items-center justify-between">
          <view>
            <view class="f-babel">
              <view class="f-label-name">计划清点：</view>
              <view>{{ taskPlanCount }}</view>
            </view>
            <view class="f-babel">
              <view class="f-label-name">已清点数：</view>
              <view>{{ taskCompletedCount }}</view>
            </view>
            <view class="f-babel">
              <view class="f-label-name">未清点数：</view>
              <view>{{ taskUnfinishedCount }}</view>
            </view>
          </view>
          <wd-circle
            class="shrink-0 mr-4"
            :strokeWidth="30"
            :color="rgbaToHex(getColor(taskCompletedPercent))"
            v-model="taskCompletedPercent"
            :text="`${taskCompletedPercent}%`"
          ></wd-circle>
        </view>
      </view>
      <view class="o-btn bg-blue color-white mt-4" @click="handleOutput">
        导出统计表
      </view>
      <view class="mt-4" v-if="successTip !== ''">
        <view class="color-gray">输出路径：</view>
        <view>{{ successTip }}</view>
      </view>
    </view>
  </view>
</template>
<style lang="scss" scoped>
.f-babel {
  @apply flex mb-2 items-baseline;
}

.f-label-name {
  @apply shrink-0 color-gray;

  //width: 24vw;
}
</style>
