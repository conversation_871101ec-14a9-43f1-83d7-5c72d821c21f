{"version": 3, "file": "index.uts", "sourceRoot": "", "sources": ["uni_modules/uts-toast/utssdk/app-android/index.uts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;AAC5C,OAAO,KAAK,MAAM,sBAAsB,CAAC;AAEzC,MAAM,UAAU,SAAS,CAAC,MAAM,EAAG,YAAY,GAAI,IAAI;IACtD,MAAM,kBAAmB,SAAQ,QAAQ;QACxC,QAAQ,CAAC,GAAG;YACX,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,EAAE,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,CAAA;QACvF,CAAC;KACD;IACD,UAAU,CAAC,cAAc,EAAE,EAAE,aAAa,CAAC,IAAI,kBAAkB,EAAE,CAAC,CAAA;AACrE,CAAC", "sourcesContent": ["import { ToastOptions } from \"../interface\";\r\nimport Toast from 'android.widget.Toast';\r\n\r\nexport function showToast(option : ToastOptions) : void {\r\n\tclass MainThreadRunnable extends Runnable {\r\n\t\toverride run() {\r\n\t\t\tToast.makeText(UTSAndroid.getUniActivity()!, option.message, Toast.LENGTH_LONG).show()\r\n\t\t}\r\n\t}\r\n\tUTSAndroid.getUniActivity()?.runOnUiThread(new MainThreadRunnable())\r\n}"]}