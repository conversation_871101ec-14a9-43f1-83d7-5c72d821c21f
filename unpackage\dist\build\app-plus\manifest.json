{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__904B921", "name": "unipluginDemo", "version": {"name": "1.0.0", "code": "100"}, "description": "", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"render": "always", "id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#F8F8F8"}, "usingComponents": true, "nvueCompiler": "uni-app", "compilerVersion": 3, "distribute": {"google": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"]}, "apple": {"dSYMs": false}, "plugins": {"push": {"fcm": {}}, "ad": {}, "audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "nativePlugins": {"yunwei1_uniplugin_rfid": {"__plugin_info__": {"name": "yunwei1_uniplugin_rfid", "description": "yunwei1_uniplugin_rfid", "platforms": "Android", "url": "", "android_package_name": "", "ios_bundle_id": "", "isCloud": false, "bought": -1, "pid": "", "parameters": {}}}}, "allowsInlineMediaPlayback": true, "uni-app": {"compilerVersion": "4.36", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal"}, "launch_path": "__uniappview.html"}}