// @import './iconfont.css';

:root,
page {
  // 修改按主题色
  --wot-color-theme: #00116f;
  --wot-color-success: #00b42a;
  --wot-color-warning: #ff7d00;
  --wot-color-danger: #dc3126;
  --wot-segmented-item-bg-color: $o-body-bg-color;
  //font-size: 14px;
  --vf-shadow-sm: 0 2vw 4vw -2vw rgba(34, 52, 122, 0.3);
  --vf-shadow-sm-blue: 0 2vw 4vw -2vw rgba(34, 52, 122, 0.8);
  --vf-shadow-sm-yellow: 0 2vw 4vw -2vw rgba(255, 125, 0, 0.8);
  --vf-shadow-sm-green: 0 2vw 4vw -2vw rgba(0, 180, 42, 0.8);
  --vf-shadow-base: 0 5vw 8vw -2vw rgba(34, 52, 122, 0.2);
  //--vf-shadow: 0 3px 1px -2px rgb(0 0 0 / 20%), 0 2px 2px 0 rgb(0 0 0 / 14%), 0 1px 5px 0 rgb(0 0 0 / 12%);

  // 修改按钮背景色
  // --wot-button-primary-bg-color: green;

  background-color: $o-body-bg-color;

  @extend %o-color-content;
}

.o-color-aid {
  color: var(--wot-color-aid, #8c8c8c);
}

%o-color-content {
  color: var(--wot-color-content, #262626);
}

.o-color-content {
  @extend %o-color-content;
}

.o-bg-no {
  background: $o-body-bg-color;
}

.o-bg-white-disable {
  background: #dbdfe4;
}

.o-bg-primary-light {
  background: #e8efff;
}

.o-color-primary {
  color: var(--wot-color-theme);
}

.o-color-success {
  color: var(--wot-color-success);
}

.o-color-warning {
  color: var(--wot-color-warning);
}

.o-color-danger {
  color: var(--wot-color-danger);
}

.o-bg-primary {
  background: linear-gradient(91deg, #16abff 0%, #165dff 37.5%, #165dff 100%);
}
.o-bg-primary-disable {
  background: #94bfff;
}

.o-bg-transparent {
  background: transparent !important;
}

.o-dot {
  $w: 10rpx;
  flex-shrink: 0;
  width: $w;
  height: $w;
  margin-top: 0.35rem;
  background: var(--wot-color-theme);
  border-radius: calc($w / 2);
}

.o-line {
  height: 1px;
  background: var(--wot-divider-line-color, rgba(0, 0, 0, 0.15));
  transform: scaleY(0.5);
}

.o-label-line {
  position: relative;

  &::after {
    position: absolute;
    right: 0;
    bottom: 3px;
    left: 0;
    height: 1px;
    content: '';
    background: var(--wot-input-border-color, #dadada);
    transform: scaleY(0.5);
  }
}

.o-shadow {
  box-shadow: var(--vf-shadow-base);
}

.o-shadow-deep {
  box-shadow: 0 10rpx 15rpx rgba(0, 0, 0, 0.2);
}

.o-btn {
  @apply rd-1 px-5 py-2 text-center;

  box-shadow: var(--vf-shadow-sm);

  &.bg-blue {
    box-shadow: var(--vf-shadow-sm-blue);
  }

  &.bg-yellow {
    box-shadow: var(--vf-shadow-sm-yellow);
  }

  &.bg-green {
    box-shadow: var(--vf-shadow-sm-green);
  }
}

.o-btn-no-style {
  padding-right: 0;
  padding-left: 0;
  margin-right: 0;
  margin-left: 0;
  background: none;

  &::after {
    border: none;
  }
}

.o-barcode-gray-card {
  padding: 0.2rem 0.5rem;
  //background-color: $o-body-bg-color;
  background-color: rgba(220, 225, 234, 0.7);
}

.o-border {
  border: 1px solid $uni-border-color;
}

.o-p {
  line-height: 1.5;
  text-indent: 2em;
}

.o-logo {
  background-image: url('https://wx.gs1helper.com/images/logo_big.png');
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
}

.o-row-scroll {
  max-width: 100%;
  overflow-x: auto;
}

.o-cs-img {
  $w: 65rpx;
  width: $w;
  height: $w;
}

.o-inline-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.o-inline-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.o-inline-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

//-------------------------------------------------------

.wd-input__clear {
  background: none !important;
}

.wd-button {
  box-shadow: var(--vf-shadow-sm);

  &.is-primary {
    box-shadow: var(--vf-shadow-sm-blue);
  }

  &.is-warning {
    box-shadow: var(--vf-shadow-sm-yellow);
  }
}

.wd-button.is-round {
  border-radius: 0.3rem !important;
}
