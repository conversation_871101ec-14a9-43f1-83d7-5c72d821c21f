{"version": 3, "file": "interface.uts", "sourceRoot": "", "sources": ["uni_modules/uts-toast/utssdk/interface.uts"], "names": [], "mappings": "AAAA,MAAM,MAAM,YAAY,GAAG;IAC1B,OAAO,EAAG,MAAM,CAAA;CAChB,CAAA;AAED,MAAM,MAAM,SAAS,GAAG,CAAC,MAAM,EAAG,YAAY,KAAK,IAAI,CAAA", "sourcesContent": ["export type ToastOptions = {\r\n\tmessage : string\r\n}\r\n\r\nexport type showToast = (option : ToastOptions) => void"]}