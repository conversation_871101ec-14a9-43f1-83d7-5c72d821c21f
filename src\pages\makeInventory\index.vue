<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '选择要清单的地点',
  },
}
</route>
<script lang="ts" setup>
import { AddressForm } from '@/store/dbStore'
import { storeToRefs } from 'pinia'
import { stepStore } from '@/store/stepStore'
import { useMessage } from 'wot-design-uni'
import { getColor, rgbaToHex } from '@/utils/colorUtils'
import { pointerStore } from '@/store/pointerStore'
import { toStepOne } from '@/utils/tool'
import { authorizationStore } from '@/store/authorizationStore'

const qrScanModule = uni.requireNativePlugin('qrScanModule') // 扫描仪Module
const modal = uni.requireNativePlugin('modal')
const globalEvent = uni.requireNativePlugin('globalEvent')

const message = useMessage()
const useStepStore = stepStore()
const { activeTaskId, activeAddressId } = storeToRefs(useStepStore)
const usePointerStore = pointerStore()
const { activeAddressList } = storeToRefs(usePointerStore)
const useAuthorizationStore = authorizationStore()
const { haveAuthorization, sn } = storeToRefs(useAuthorizationStore)

const initQrScan = () => {
  // 初始化扫描仪
  qrScanModule.initQrScan({}, (ret) => {
    if (!ret.success) {
      modal.toast({
        message: '扫描枪初始化失败',
        duration: 1.5,
      })
    }
  })
}

const listenQrCode = () => {
  globalEvent.addEventListener('RendQrScanEvent', function (e) {
    /*    modal.toast({
          message: "RendQrScanEvent收到：" + JSON.stringify(e),
          duration: 1.5
        }); */
    // 去掉字符串e.result末尾的换行符
    const name = e.result.replace(/\r/g, '')
    const addressItem = activeAddressList.value.find(
      (item) => item.name === name,
    )
    if (addressItem) {
      startCheck(addressItem.id)
    } else {
      message.alert({
        msg: e.result,
        title: '没有找到该地址',
      })
    }
  })
}

const listenKeyCode = () => {
  plus.key.addEventListener('keydown', (event) => {
    console.log('按下按键:' + event.keyCode)
    // 触发RFID枪按键
    if (event.keyCode === 138) {
      // console.log('按下开枪键')
      toScan()
    }
  })
}

/* const linkDevice = () => {
  const r = rfidModule.getAntiWRPower({})
  console.log(r)
  const { success: haveLinked } = r
  if (!haveLinked) {
    setTimeout(() => {
      rfidModule.toConnectDevice({}, (ret) => {
        console.log(ret)
        if (ret.success) {
          sn.value = ret.sn
          if (!OKSN.includes(ret.sn)) {
            rfidModule.toDisConnectDevice({}, (ret) => {})
            showOverlay.value = true
          }
        }
        /!*      modal.toast({
          message: ret,
          duration: 1.5,
        }) *!/
      })
    }, 800)
  }
} */

onShow(() => {
  initQrScan()
  listenQrCode()
  listenKeyCode()

  // 先开RFID，为下一阶段做准备，不用频繁开关
  // 也需要验证sn
  // linkDevice()
})

onHide(() => {
  // console.log('makeInve,onHide')
  removeEventListenerAll()
})

const toScan = () => {
  /*  uni.scanCode({
    onlyFromCamera: true,
    success: function (res) {
      console.log('条码类型：' + res.scanType)
      console.log('条码内容：' + res.result)
      const addressItem = activeAddressList.value.find(
        (item) => item.name === res.result,
      )
      if (addressItem) {
        activeAddressId.value = addressItem.id
        scanStep.value = ScanStep.startCheck
      } else {
        message.alert({
          msg: res.result,
          title: '没有找到该地址',
        })
      }
    },
  }) */
  qrScanModule.toScan({}, () => {
    // 等listenQrcode监听到扫码事件后，再执行写RFID操作
  })
}

const handleResetCheck = (data: AddressForm) => {
  message
    .confirm({
      msg: '将会重置本地点清点数据',
      title: '是否重新清点',
    })
    .then(() => {
      data.completedCodeArr = []
      data.unfinishedCodeArr = [...data.planCodeArr]
      data.unplannedCodeArr = []
      if (data.planCodeArr.length === 0) {
        data.completedPercent = 100
      } else {
        data.completedPercent = 0
      }
    })
    .catch(() => {})
}

const removeEventListenerAll = () => {
  plus.key.removeEventListener('keydown', () => {})
  globalEvent.removeEventListener('RendQrScanEvent', () => {})
}

const startCheck = (addressId: string) => {
  removeEventListenerAll()
  // 有授权才能使用
  if (haveAuthorization.value) {
    activeAddressId.value = addressId
    uni.navigateTo({
      url: '/pages/startCheckPage/index',
    })
  }
}
</script>
<template>
  <wd-overlay :show="!haveAuthorization">
    <view class="color-white text-center text-4xl mt-40">设备未授权</view>
    <view class="color-white text-center">sn：{{ sn }}</view>
  </wd-overlay>
  <view class="w-full overflow-hidden">
    <view v-if="activeTaskId === ''" class="f-no center">
      <view class="o-btn bg-blue color-white text-lg" @click="toStepOne">
        请先新建或选择任务
      </view>
    </view>
    <view
      v-else
      class="w-full bg-white fixed top-0 left-0 o-shadow"
      style="z-index: 3"
    >
      <view class="flex p-4">
        <!--      <wd-button class="grow-1" @click="toTestPage">扫场所码</wd-button>-->
        <wd-button class="grow-1" @click="toScan">
          按下扳机键开始扫场所码
        </wd-button>
      </view>
    </view>
    <view class="pt-20 px-3 pb-20">
      <view
        v-for="item in activeAddressList"
        :key="item?.id"
        :style="{
          backgroundColor:
            item?.unplannedCodeArr?.length > 0 ? '#ffe5e5' : '#fff',
        }"
        class="rd-2 mb-2 o-shadow"
      >
        <view class="p-4">
          <view class="flex items-center gap-2">
            <wd-icon name="home" size="22px"></wd-icon>
            <view class="f-h-name font-bold text-xl my-2 o-inline-2">
              {{ item?.name }}
            </view>
          </view>
          <view class="flex items-center justify-between mt-2">
            <view>
              <view class="f-babel">
                <view class="f-label-name">计划清点：</view>
                <view>{{ item?.planCodeArr?.length }}</view>
              </view>
              <view class="f-babel">
                <view class="f-label-name">已清点：</view>
                <view>{{ item?.completedCodeArr?.length }}</view>
              </view>
              <view class="f-babel">
                <view class="f-label-name">未清点：</view>
                <view>{{ item?.unfinishedCodeArr?.length }}</view>
              </view>
              <view class="f-babel">
                <view class="f-label-name">额外发现：</view>
                <view
                  :class="item?.unplannedCodeArr?.length > 0 ? 'color-red' : ''"
                >
                  {{ item?.unplannedCodeArr?.length }}
                </view>
              </view>
            </view>
            <wd-circle
              v-model="item.completedPercent"
              :color="rgbaToHex(getColor(item.completedPercent))"
              :strokeWidth="30"
              :text="`${item.completedPercent}%`"
              class="shrink-0 mr-4"
            ></wd-circle>
          </view>
        </view>
        <view class="flex justify-between items-center mt-1 px-4 pb-4">
          <view
            class="o-btn mr-2 o-border color-red bg-white"
            @click="handleResetCheck(item)"
          >
            重新清点
          </view>
          <view
            class="o-btn bg-blue color-white grow-1"
            @click="startCheck(item.id)"
          >
            开始清点
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<style lang="scss" scoped>
.f-no {
  height: 100vh;
}

.f-babel {
  @apply flex mb-1 items-baseline;
}

.f-label-name {
  @apply shrink-0 color-gray;

  width: 24vw;
}

.f-h-name {
  max-height: 2.6em;
  overflow: hidden !important;
}
</style>
