<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  style: {
    navigationBarTitleText: '建立数据',
  },
}
</route>
<script lang="ts" setup>
import NewTaskPage from '@/components/index/newTaskPage.vue'
import { stepStore } from '@/store/stepStore'
import { storeToRefs } from 'pinia'
import { CreateStep } from '@/utils/enum'
import NewPrintRfidPage from '@/components/index/newPrintRfidPage.vue'
import { authorizationStore } from '@/store/authorizationStore'

defineOptions({
  name: 'Home',
})
const useAuthorizationStore = authorizationStore()
const { haveAuthorization, sn } = storeToRefs(useAuthorizationStore)

const useStepStore = stepStore()
const { createStep } = storeToRefs(useStepStore)

// onShow(() => {
//   // 本页关闭RFID
//   rfidModule.toDisConnectDevice({}, (ret) => {})
// })

watch(
  createStep,
  () => {
    if (createStep.value === CreateStep.createTask) {
      uni.setNavigationBarTitle({
        title: '新建/选择任务',
      })
    } else if (createStep.value === CreateStep.createScanList) {
      uni.setNavigationBarTitle({
        title: '资产清单准备',
      })
    }
  },
  { immediate: true },
)
</script>
<template>
  <view v-if="haveAuthorization" class="w-full overflow-hidden">
    <NewTaskPage v-if="createStep === CreateStep.createTask" />
    <NewPrintRfidPage v-else-if="createStep === CreateStep.createScanList" />
  </view>
  <wd-overlay :show="!haveAuthorization">
    <view class="color-white text-center text-4xl mt-40">设备未授权</view>
    <view class="color-white text-center">sn：{{ sn }}</view>
  </wd-overlay>
</template>
<style lang="scss" scoped></style>
