<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { OKSN } from '@/utils/enum'
import { storeToRefs } from 'pinia'
import { authorizationStore } from '@/store/authorizationStore'

const useAuthorizationStore = authorizationStore()
const { haveAuthorization, sn } = storeToRefs(useAuthorizationStore)
const rfidModule = uni.requireNativePlugin('rfidModule')

const linkDevice = () => {
  const r = rfidModule.getAntiWRPower({})
  console.log(r)
  const { success: haveLinked } = r
  if (!haveLinked) {
    rfidModule.toConnectDevice({}, (ret) => {
      console.log(ret)
      if (ret.success) {
        sn.value = ret.sn
        if (OKSN.includes(ret.sn)) {
          haveAuthorization.value = true
        } else {
          // TODO dev 授权测试
          if (import.meta.env.MODE === 'development') {
            haveAuthorization.value = true
            console.log('开发环境下授权测试模式已启用')
          } else {
            haveAuthorization.value = false
            rfidModule.toDisConnectDevice({}, (ret) => {})
          }
        }
      }
    })
  }
}

onLaunch(() => {
  // console.log('App Launch')
  uni.getStorage({
    key: 'o_powerValue',
    success: function (res) {
      console.log(res.data)
    },
    fail: function (err) {
      console.log(err)
      const powerValue = '500,2000,3000'
      try {
        uni.setStorageSync('o_powerValue', powerValue)
      } catch (e) {}
    },
  })
})
onShow(() => {
  linkDevice()
  console.log('App Show')
})
onHide(() => {
  console.log('App Hide')
  rfidModule.toDisConnectDevice({}, (ret) => {
    console.log(ret)
  })
})
</script>

<style lang="scss">
/* stylelint-disable selector-type-no-unknown */
button::after {
  border: none;
}

swiper,
scroll-view {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

// 单行省略，优先使用 unocss: text-ellipsis
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 两行省略
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 三行省略
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style>
